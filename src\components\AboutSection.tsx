import React from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';

const AboutSection = () => {
  const { t } = useTranslation('common');
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center gap-12">
          <div className="md:w-1/2">
            <img
              src="https://readdy.ai/api/search-image?query=professional%20hotel%20and%20restaurant%20equipment%20supplier%20team%2C%20diverse%20staff%20in%20formal%20attire%2C%20hospitality%20industry%20professionals%2C%20business%20meeting%2C%20showroom%20with%20hotel%20supplies%2C%20customer%20service%20representatives&width=600&height=400&seq=4&orientation=landscape"
              alt={t('about.alt')}
              className="w-full h-auto rounded-lg shadow-lg"
            />
          </div>
          <div className="md:w-1/2">
            <h2 className="text-3xl font-bold text-primary mb-6">
              {t('about.title')}
            </h2>
            <p className="text-gray-700 mb-6">
              {t('about.description1')}
            </p>
            <p className="text-gray-700 mb-8">
              {t('about.description2')}
            </p>
            <div className="flex gap-4">
              <Link
                href="/about"
                className="bg-primary text-white px-6 py-3 rounded-button font-medium hover:bg-primary/90 transition-colors whitespace-nowrap"
              >
                {t('about.cta')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
