/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // إعدادات الأمان الأساسية
  poweredByHeader: false,

  // إعدادات الصور
  images: {
    unoptimized: true,
  },

  // إعدادات ESLint و TypeScript للتطوير
  eslint: {
    ignoreDuringBuilds: false,
  },

  typescript: {
    ignoreBuildErrors: false,
  },

  // Headers الأمان الأساسية
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
