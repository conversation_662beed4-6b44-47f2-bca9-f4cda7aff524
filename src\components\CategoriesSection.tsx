import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import { Category } from '../types/database';
import { categoriesApi } from '../lib/api';

const CategoriesSection = () => {
  const { t } = useTranslation('common');
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const categoriesData = await categoriesApi.getAll();
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-primary mb-4">{t('categories.title')}</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              {t('categories.subtitle')}
            </p>
          </div>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل الفئات...</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-primary mb-4">{t('categories.title')}</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {t('categories.subtitle')}
          </p>
        </div>
        {categories.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {categories.map((category) => (
              <Link
                href={`/products?category=${category.id}`}
                key={category.id}
                className="category-item relative rounded-lg overflow-hidden shadow-md group"
              >
                <img
                  src={category.image || 'https://via.placeholder.com/400x300'}
                  alt={category.nameAr}
                  className="w-full h-64 object-cover object-top transition-transform duration-300 group-hover:scale-105"
                />
                <div className="category-overlay absolute inset-0 bg-primary/70 flex items-center justify-center opacity-0 transition-opacity duration-300">
                  <h3 className="text-white text-xl font-bold">{category.nameAr}</h3>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                  <h3 className="text-primary text-lg font-bold">{category.nameAr}</h3>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-600">لا توجد فئات متاحة حالياً</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default CategoriesSection;
